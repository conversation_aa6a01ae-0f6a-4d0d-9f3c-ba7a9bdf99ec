@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
@import "tailwindcss";

@layer base {
  body {
    background-color: #0A0A0A;
    color: #F3F4F6;
    font-family: 'Inter', sans-serif;
  }
  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  .glass-effect {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(12px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  .gradient-text {
    background: linear-gradient(to right, #A78BFA, #C4B5FD);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  .section-padding {
    padding: 6rem 1rem; /* Aumentar padding para más espacio en blanco */
  }
}