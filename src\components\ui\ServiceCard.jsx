const ServiceCard = ({ icon, title, description, delay = 0 }) => {
  return (
    <div
      className="glass-effect p-8 rounded-2xl transition-all duration-300 hover:-translate-y-2 group animate-fade-in-up"
      style={{ animationDelay: `${delay}ms` }}
    >
      <div className="mb-6">
        <div className="text-accent mb-4">
          {icon}
        </div>
      </div>

      <h3 className="text-xl font-bold mb-4 text-text-primary">
        {title}
      </h3>

      <p className="text-text-secondary leading-relaxed">
        {description}
      </p>
    </div>
  );
};

export default ServiceCard;
