import { caseStudies } from '../../constants.jsx';
import SectionHeader from '../ui/SectionHeader';
import CaseStudyCard from '../ui/CaseStudyCard';

const CaseStudies = () => {
  return (
    <section id="case-studies" className="section-padding">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <SectionHeader
          title="Clientes que ya están en el Futuro"
          subtitle="Descubre cómo nuestras soluciones de IA han transformado negocios reales, generando resultados medibles y un impacto duradero."
        />

        <div className="space-y-16">
          {caseStudies.map((study, index) => (
            <CaseStudyCard
              key={study.id}
              image={study.image}
              title={study.title}
              description={study.description}
              quote={study.quote}
              author={study.author}
              metrics={study.metrics}
              reverse={index % 2 !== 0}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default CaseStudies;
