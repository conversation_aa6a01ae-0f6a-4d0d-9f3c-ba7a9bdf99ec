import { services } from '../../constants.jsx';
import SectionHeader from '../ui/SectionHeader';
import ServiceCard from '../ui/ServiceCard';

const Services = () => {
  return (
    <section id="services" className="section-padding">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <SectionHeader
          title="Nuestros Servicios de Automatización"
          subtitle="Transformamos tu negocio con soluciones de IA personalizadas que impulsan el crecimiento y optimizan la eficiencia operativa."
        />

        <div className="grid md:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <ServiceCard
              key={service.id}
              icon={service.icon}
              title={service.title}
              description={service.description}
              delay={index * 200}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default Services;
