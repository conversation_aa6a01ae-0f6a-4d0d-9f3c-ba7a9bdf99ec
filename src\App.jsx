
import Navbar from './components/layout/Navbar';
import Hero from './components/sections/Hero';
import Services from './components/sections/Services';
import CaseStudies from './components/sections/CaseStudies';
import ContactCTA from './components/sections/ContactCTA';
import Footer from './components/layout/Footer';

function App() {
  return (
    <div className="relative bg-background text-text-primary font-sans">
      <div className="absolute inset-0 -z-10 h-full w-full bg-gradient-to-br from-background via-primary to-background bg-[length:200%_200%] animate-background-pan"></div>
      <Navbar />
      <main>
        <Hero />
        <Services />
        <CaseStudies />
        <ContactCTA />
      </main>
      <Footer />
    </div>
  );
}

export default App;
