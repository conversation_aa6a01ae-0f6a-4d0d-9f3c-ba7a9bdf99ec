const CaseStudyCard = ({ 
  image, 
  title, 
  description, 
  quote, 
  author, 
  metrics = [], 
  reverse = false 
}) => {
  return (
    <div className="mb-20 animate-fade-in-up">
      <div className="grid md:grid-cols-2 gap-12 items-center">
        {/* Image */}
        <div className={`${reverse ? 'md:order-last' : ''}`}>
          <div className="relative group">
            <img 
              src={image} 
              alt={title}
              className="rounded-lg shadow-lg w-full h-64 object-cover transition-transform duration-300 group-hover:scale-105"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-accent/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg"></div>
          </div>
        </div>
        
        {/* Content */}
        <div className={`${reverse ? 'md:order-first' : ''}`}>
          <h3 className="text-2xl font-bold mb-4 text-text-primary">
            {title}
          </h3>
          
          <p className="text-text-secondary mb-6 text-lg leading-relaxed">
            {description}
          </p>
          
          {/* Metrics */}
          {metrics.length > 0 && (
            <div className="flex space-x-8 mt-6 border-t border-white/10 pt-4 mb-6">
              {metrics.map((metric, index) => (
                <div key={index}>
                  <div className="text-2xl font-bold text-accent">
                    {metric.value}
                  </div>
                  <div className="text-sm text-text-secondary tracking-wide">
                    {metric.label}
                  </div>
                </div>
              ))}
            </div>
          )}
          
          {/* Quote */}
          <blockquote className="border-l-4 border-accent pl-4 italic text-accent bg-accent/5 p-4 rounded-r-lg">
            <p className="mb-2">"{quote}"</p>
            <cite className="text-sm text-text-secondary not-italic">
              — {author}
            </cite>
          </blockquote>
        </div>
      </div>
    </div>
  );
};

export default CaseStudyCard;
