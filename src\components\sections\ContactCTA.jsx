import { contactContent } from '../../constants.jsx';
import SectionHeader from '../ui/SectionHeader';
import AnimatedButton from '../ui/AnimatedButton';

const ContactCTA = () => {
  return (
    <section id="contact" className="section-padding">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-3xl mx-auto text-center glass-effect p-10 rounded-2xl">
          <SectionHeader
            title={contactContent.title}
            subtitle={contactContent.subtitle}
          />
          <AnimatedButton
            href={`mailto:${contactContent.email}`}
            size="lg"
            className="animate-glow"
          >
            {contactContent.ctaText}
          </AnimatedButton>
        </div>
      </div>
    </section>
  );
};

export default ContactCTA;
