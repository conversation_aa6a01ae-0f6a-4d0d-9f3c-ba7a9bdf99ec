import { heroContent } from '../../constants.jsx';
import AnimatedButton from '../ui/AnimatedButton';

const Hero = () => {
  const scrollToSection = (sectionId) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section className="relative min-h-screen flex items-center justify-center">
      <div className="text-center max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
        <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 animate-fade-in-up">
          {heroContent.title}{' '}
          <span className="gradient-text">
            {heroContent.titleAccent}
          </span>
        </h1>

        <p className="text-xl md:text-2xl text-text-secondary mb-12 max-w-3xl mx-auto leading-relaxed animate-fade-in-up">
          {heroContent.subtitle}
        </p>

        <div className="animate-fade-in-up">
          <AnimatedButton
            onClick={() => scrollToSection('services')}
            size="lg"
          >
            {heroContent.ctaText}
          </AnimatedButton>
        </div>
      </div>
    </section>
  );
};

export default Hero;
